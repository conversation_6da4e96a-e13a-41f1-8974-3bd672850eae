/**
 * PWA Test Page
 * Ocean Soul Sparkles - Test PWA functionality and features
 */

import { useState, useEffect } from 'react'
import Head from 'next/head'
import { usePWA } from '@/lib/hooks/usePWA'
import { usePWAContext } from '@/components/PWAProvider'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import PWAInstallPrompt, { PWAStatusIndicator, PWAInstallInstructions } from '@/components/PWAInstallPrompt'
import CameraCapture from '@/components/CameraCapture'
import styles from '@/styles/PWATest.module.css'

export default function PWATestPage() {
  const [showCamera, setShowCamera] = useState(false)
  const [capturedPhotos, setCapturedPhotos] = useState([])
  const [testResults, setTestResults] = useState({})
  const [isRunningTests, setIsRunningTests] = useState(false)

  const pwa = usePWA()
  const pwaContext = usePWAContext()
  const mobile = useMobileOptimization()

  // Run PWA feature tests
  const runPWATests = async () => {
    setIsRunningTests(true)
    const results = {}

    try {
      // Test service worker
      results.serviceWorker = 'serviceWorker' in navigator
      
      // Test manifest
      try {
        const manifestResponse = await fetch('/manifest.json')
        results.manifest = manifestResponse.ok
      } catch {
        results.manifest = false
      }

      // Test offline storage
      try {
        await pwaContext.cacheEssentialData()
        results.offlineStorage = true
      } catch {
        results.offlineStorage = false
      }

      // Test device capabilities
      const capabilities = pwa.getDeviceCapabilities()
      results.capabilities = capabilities

      // Test background sync
      results.backgroundSync = pwa.isFeatureSupported('backgroundSync')

      // Test camera
      results.camera = pwa.isFeatureSupported('camera')

      // Test push notifications
      results.pushNotifications = pwa.isFeatureSupported('pushNotifications')

      // Test web share
      results.webShare = pwa.isFeatureSupported('share')

      setTestResults(results)
    } catch (error) {
      console.error('PWA tests failed:', error)
    } finally {
      setIsRunningTests(false)
    }
  }

  // Handle photo capture
  const handlePhotoCapture = (photoData) => {
    setCapturedPhotos(prev => [...prev, photoData])
    setShowCamera(false)
  }

  // Test offline functionality
  const testOfflineFeatures = async () => {
    try {
      // Test offline booking creation
      const testBooking = {
        service: { id: 'test', name: 'Test Service' },
        artist: { id: 'test', name: 'Test Artist' },
        customer: { name: 'Test Customer', email: '<EMAIL>' },
        timeSlot: { time: new Date().toISOString() }
      }

      await pwaContext.addToOfflineQueue('booking', testBooking, 'high')
      alert('Test booking added to offline queue!')
    } catch (error) {
      alert('Offline test failed: ' + error.message)
    }
  }

  // Test share functionality
  const testShare = async () => {
    const shareData = {
      title: 'Ocean Soul Sparkles PWA Test',
      text: 'Testing the PWA share functionality',
      url: window.location.href
    }

    const success = await pwa.shareContent(shareData)
    if (!success) {
      alert('Share not supported or failed')
    }
  }

  useEffect(() => {
    // Auto-run tests on mount
    runPWATests()
  }, [])

  return (
    <>
      <Head>
        <title>PWA Test - Ocean Soul Sparkles</title>
        <meta name="description" content="Test Progressive Web App functionality" />
      </Head>

      <div className={styles.container}>
        <header className={styles.header}>
          <h1>PWA Test Page</h1>
          <p>Test Progressive Web App functionality for Ocean Soul Sparkles</p>
          
          <PWAStatusIndicator showDetails={true} />
        </header>

        <main className={styles.main}>
          {/* PWA Status */}
          <section className={styles.section}>
            <h2>PWA Status</h2>
            <div className={styles.statusGrid}>
              <div className={styles.statusItem}>
                <span className={styles.label}>Online:</span>
                <span className={`${styles.status} ${pwa.isOnline ? styles.success : styles.error}`}>
                  {pwa.isOnline ? 'Yes' : 'No'}
                </span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.label}>Installed:</span>
                <span className={`${styles.status} ${pwa.isInstalled ? styles.success : styles.info}`}>
                  {pwa.isInstalled ? 'Yes' : 'No'}
                </span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.label}>Installable:</span>
                <span className={`${styles.status} ${pwa.isInstallable ? styles.success : styles.info}`}>
                  {pwa.isInstallable ? 'Yes' : 'No'}
                </span>
              </div>
              <div className={styles.statusItem}>
                <span className={styles.label}>Update Available:</span>
                <span className={`${styles.status} ${pwa.updateAvailable ? styles.warning : styles.success}`}>
                  {pwa.updateAvailable ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </section>

          {/* Device Info */}
          <section className={styles.section}>
            <h2>Device Information</h2>
            <div className={styles.deviceInfo}>
              <p><strong>Mobile:</strong> {mobile.isMobile ? 'Yes' : 'No'}</p>
              <p><strong>Touch Device:</strong> {mobile.isTouchDevice ? 'Yes' : 'No'}</p>
              <p><strong>Orientation:</strong> {mobile.orientation}</p>
              <p><strong>Screen Size:</strong> {mobile.screenWidth} x {mobile.screenHeight}</p>
              <p><strong>Pixel Ratio:</strong> {mobile.pixelRatio}</p>
              <p><strong>Connection:</strong> {mobile.connectionType}</p>
            </div>
          </section>

          {/* Feature Tests */}
          <section className={styles.section}>
            <h2>Feature Tests</h2>
            <button 
              className={styles.button}
              onClick={runPWATests}
              disabled={isRunningTests}
            >
              {isRunningTests ? 'Running Tests...' : 'Run PWA Tests'}
            </button>

            {Object.keys(testResults).length > 0 && (
              <div className={styles.testResults}>
                <h3>Test Results:</h3>
                {Object.entries(testResults).map(([key, value]) => (
                  <div key={key} className={styles.testResult}>
                    <span className={styles.testName}>{key}:</span>
                    <span className={`${styles.testValue} ${
                      typeof value === 'boolean' 
                        ? (value ? styles.success : styles.error)
                        : styles.info
                    }`}>
                      {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </section>

          {/* Action Tests */}
          <section className={styles.section}>
            <h2>Action Tests</h2>
            <div className={styles.actionGrid}>
              {pwa.isInstallable && (
                <button className={styles.button} onClick={pwa.installApp}>
                  Install PWA
                </button>
              )}
              
              {pwa.updateAvailable && (
                <button className={styles.button} onClick={pwa.updateApp}>
                  Update App
                </button>
              )}

              <button className={styles.button} onClick={testOfflineFeatures}>
                Test Offline Queue
              </button>

              <button className={styles.button} onClick={testShare}>
                Test Share
              </button>

              {pwa.isFeatureSupported('camera') && (
                <button className={styles.button} onClick={() => setShowCamera(true)}>
                  Test Camera
                </button>
              )}

              {mobile.isTouchDevice && (
                <button 
                  className={styles.button} 
                  onClick={() => mobile.hapticFeedback('medium')}
                >
                  Test Haptic Feedback
                </button>
              )}
            </div>
          </section>

          {/* Captured Photos */}
          {capturedPhotos.length > 0 && (
            <section className={styles.section}>
              <h2>Captured Photos</h2>
              <div className={styles.photoGrid}>
                {capturedPhotos.map((photo, index) => (
                  <div key={index} className={styles.photoItem}>
                    <img src={photo.url} alt={`Captured ${index + 1}`} />
                    <p>Type: {photo.type}</p>
                    <p>Size: {(photo.blob.size / 1024).toFixed(1)} KB</p>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Installation Instructions */}
          {!pwa.isInstalled && (
            <section className={styles.section}>
              <h2>Installation Instructions</h2>
              <PWAInstallInstructions />
            </section>
          )}

          {/* Storage Stats */}
          <section className={styles.section}>
            <h2>Storage Information</h2>
            <button 
              className={styles.button}
              onClick={async () => {
                const stats = await pwaContext.getStorageStats()
                alert('Storage Stats:\n' + JSON.stringify(stats, null, 2))
              }}
            >
              Get Storage Stats
            </button>
          </section>
        </main>

        {/* Camera Modal */}
        {showCamera && (
          <CameraCapture
            type="portfolio"
            onCapture={handlePhotoCapture}
            onClose={() => setShowCamera(false)}
            maxWidth={1280}
            maxHeight={720}
            quality={0.8}
          />
        )}

        {/* Install Prompt */}
        <PWAInstallPrompt 
          showOnMobile={true}
          showOnDesktop={true}
          position="bottom-center"
          autoShow={false}
        />
      </div>
    </>
  )
}
